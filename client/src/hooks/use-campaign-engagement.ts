import { useQuery } from "@tanstack/react-query";

export interface CampaignEngagementData {
  name: string; // Time period name (Mon, Week 1, Jan, etc.)
  [mediaName: string]: string | number; // Dynamic keys for each media item
}

export interface MediaInfo {
  name: string;
  color: string;
}

export function useCampaignEngagement(teamId: string, timeRange: 'week' | 'month' | 'year') {
  const {
    data: rawData,
    isLoading,
    error,
  } = useQuery<any[]>({
    queryKey: [`/api/teams/${teamId}/campaign-log-summary`, timeRange],
    queryFn: async () => {
      const response = await fetch(`/api/teams/${teamId}/campaign-log-summary?timeRange=${timeRange}`);
      if (!response.ok) {
        throw new Error('Failed to fetch campaign engagement data');
      }
      return response.json();
    },
    enabled: !!teamId,
  });

  // Transform the raw data into chart-friendly format
  const { data, mediaItems } = rawData ? transformData(rawData, timeRange) : { data: [], mediaItems: [] };

  // Debug logging
  console.log('Campaign Engagement Debug:', {
    timeRange,
    rawDataLength: rawData?.length || 0,
    rawDataSample: rawData?.slice(0, 3),
    transformedDataLength: data.length,
    transformedDataSample: data.slice(0, 3),
    mediaItemsCount: mediaItems.length
  });

  return {
    data,
    mediaItems,
    isLoading,
    error,
  };
}

function transformData(rawData: any[], timeRange: 'week' | 'month' | 'year'): {
  data: CampaignEngagementData[],
  mediaItems: MediaInfo[]
} {
  if (!rawData || rawData.length === 0) {
    return getDefaultData(timeRange);
  }

  // Get unique media names
  const mediaNames = Array.from(new Set(rawData.map(item => item.media_name).filter(Boolean)));

  // Create color palette for media items
  const colors = [
    'hsl(var(--chart-1))',
    'hsl(var(--chart-2))',
    'hsl(var(--chart-3))',
    'hsl(var(--chart-4))',
    'hsl(var(--chart-5))',
    '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00',
    '#ff00ff', '#00ffff', '#ffff00', '#ff0000', '#0000ff'
  ];

  const mediaItems: MediaInfo[] = mediaNames.map((name, index) => ({
    name,
    color: colors[index % colors.length]
  }));

  let transformedData: CampaignEngagementData[] = [];

  switch (timeRange) {
    case 'week':
      transformedData = transformWeekData(rawData, mediaNames);
      break;
    case 'month':
      transformedData = transformMonthData(rawData, mediaNames);
      break;
    case 'year':
      transformedData = transformYearData(rawData, mediaNames);
      break;
  }

  return { data: transformedData, mediaItems };
}

function transformWeekData(rawData: any[], mediaNames: string[]): CampaignEngagementData[] {
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const weekData: CampaignEngagementData[] = [];

  // Create data structure for each day of the week
  for (let dow = 0; dow <= 6; dow++) {
    const dayData: CampaignEngagementData = {
      name: dayNames[dow]
    };

    // Add impression count for each media item for this day
    mediaNames.forEach(mediaName => {
      const impressions = rawData
        .filter(item => {
          if (item.media_name !== mediaName) return false;
          const logDate = new Date(item.log_date);
          return logDate.getDay() === dow;
        })
        .reduce((sum, item) => sum + (parseInt(item.total_impressions) || 0), 0);

      dayData[mediaName] = impressions;
    });

    weekData.push(dayData);
  }

  return weekData;
}

function transformMonthData(rawData: any[], mediaNames: string[]): CampaignEngagementData[] {
  const monthData: CampaignEngagementData[] = [];

  // Get current month and year
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();

  // Helper function to get week number of month (1-4)
  const getWeekOfMonth = (date: Date): number => {
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const dayOfMonth = date.getDate();
    const dayOfWeek = firstDay.getDay();
    return Math.ceil((dayOfMonth + dayOfWeek) / 7);
  };

  // Filter data for current month
  const currentMonthData = rawData.filter(item => {
    const logDate = new Date(item.log_date);
    return logDate.getMonth() === currentMonth && logDate.getFullYear() === currentYear;
  });

  // Create data for weeks 1-4
  for (let week = 1; week <= 4; week++) {
    const weekData: CampaignEngagementData = {
      name: `Week ${week}`
    };

    // Add impression count for each media item for this week
    mediaNames.forEach(mediaName => {
      const impressions = currentMonthData
        .filter(item => {
          if (item.media_name !== mediaName) return false;
          const logDate = new Date(item.log_date);
          return getWeekOfMonth(logDate) === week;
        })
        .reduce((sum, item) => sum + (parseInt(item.total_impressions) || 0), 0);

      weekData[mediaName] = impressions;
    });

    monthData.push(weekData);
  }

  return monthData;
}

function transformYearData(rawData: any[], mediaNames: string[]): CampaignEngagementData[] {
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const yearData: CampaignEngagementData[] = [];

  // Get current year
  const currentYear = new Date().getFullYear();

  // Create data for each month
  for (let month = 0; month <= 11; month++) {
    const monthData: CampaignEngagementData = {
      name: monthNames[month]
    };

    // Add impression count for each media item for this month
    mediaNames.forEach(mediaName => {
      const impressions = rawData
        .filter(item => {
          if (item.media_name !== mediaName) return false;
          const logDate = new Date(item.log_date);
          return logDate.getMonth() === month && logDate.getFullYear() === currentYear;
        })
        .reduce((sum, item) => sum + (parseInt(item.total_impressions) || 0), 0);

      monthData[mediaName] = impressions;
    });

    yearData.push(monthData);
  }

  return yearData;
}

function getDefaultData(timeRange: 'week' | 'month' | 'year'): {
  data: CampaignEngagementData[],
  mediaItems: MediaInfo[]
} {
  const mediaItems: MediaInfo[] = [
    { name: "Summer Sale Banner", color: 'hsl(var(--chart-1))' },
    { name: "Product Demo Video", color: 'hsl(var(--chart-2))' },
    { name: "Welcome Message", color: 'hsl(var(--chart-3))' },
  ];

  let data: CampaignEngagementData[] = [];

  switch (timeRange) {
    case 'week':
      data = [
        { name: "Mon", "Summer Sale Banner": 2400, "Product Demo Video": 1800, "Welcome Message": 1200 },
        { name: "Tue", "Summer Sale Banner": 1398, "Product Demo Video": 2100, "Welcome Message": 1500 },
        { name: "Wed", "Summer Sale Banner": 9800, "Product Demo Video": 1600, "Welcome Message": 2200 },
        { name: "Thu", "Summer Sale Banner": 3908, "Product Demo Video": 2800, "Welcome Message": 1800 },
        { name: "Fri", "Summer Sale Banner": 4800, "Product Demo Video": 2200, "Welcome Message": 1600 },
        { name: "Sat", "Summer Sale Banner": 3800, "Product Demo Video": 1900, "Welcome Message": 1400 },
        { name: "Sun", "Summer Sale Banner": 4300, "Product Demo Video": 2400, "Welcome Message": 1700 },
      ];
      break;
    case 'month':
      data = [
        { name: "Week 1", "Summer Sale Banner": 15000, "Product Demo Video": 9800, "Welcome Message": 7200 },
        { name: "Week 2", "Summer Sale Banner": 18000, "Product Demo Video": 12000, "Welcome Message": 8500 },
        { name: "Week 3", "Summer Sale Banner": 16500, "Product Demo Video": 10300, "Welcome Message": 7800 },
        { name: "Week 4", "Summer Sale Banner": 19200, "Product Demo Video": 13500, "Welcome Message": 9100 },
      ];
      break;
    case 'year':
      data = [
        { name: "Jan", "Summer Sale Banner": 65000, "Product Demo Video": 45000, "Welcome Message": 32000 },
        { name: "Feb", "Summer Sale Banner": 72000, "Product Demo Video": 52000, "Welcome Message": 38000 },
        { name: "Mar", "Summer Sale Banner": 79000, "Product Demo Video": 58000, "Welcome Message": 42000 },
        { name: "Apr", "Summer Sale Banner": 85000, "Product Demo Video": 63000, "Welcome Message": 46000 },
        { name: "May", "Summer Sale Banner": 81000, "Product Demo Video": 61000, "Welcome Message": 44000 },
        { name: "Jun", "Summer Sale Banner": 87000, "Product Demo Video": 67000, "Welcome Message": 48000 },
        { name: "Jul", "Summer Sale Banner": 93000, "Product Demo Video": 72000, "Welcome Message": 52000 },
        { name: "Aug", "Summer Sale Banner": 96000, "Product Demo Video": 75000, "Welcome Message": 54000 },
        { name: "Sep", "Summer Sale Banner": 98000, "Product Demo Video": 78000, "Welcome Message": 56000 },
        { name: "Oct", "Summer Sale Banner": 102000, "Product Demo Video": 82000, "Welcome Message": 58000 },
        { name: "Nov", "Summer Sale Banner": 110000, "Product Demo Video": 88000, "Welcome Message": 62000 },
        { name: "Dec", "Summer Sale Banner": 118000, "Product Demo Video": 93000, "Welcome Message": 66000 },
      ];
      break;
  }

  return { data, mediaItems };
}
