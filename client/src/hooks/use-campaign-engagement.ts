import { useQuery } from "@tanstack/react-query";

export interface CampaignEngagementData {
  name: string; // Time period name (Mon, Week 1, Jan, etc.)
  [mediaName: string]: string | number; // Dynamic keys for each media item
}

export interface MediaInfo {
  name: string;
  color: string;
}

interface RawTimeseriesData {
  period: string;
  media_name: string;
  total_impressions: number;
}

export function useCampaignEngagement(teamId: string, timeRange: 'week' | 'month' | 'year') {
  const {
    data: rawData,
    isLoading,
    error,
  } = useQuery<RawTimeseriesData[]>({
    queryKey: [`/api/teams/${teamId}/campaign-summary-timeseries`, timeRange],
    queryFn: async () => {
      const response = await fetch(`/api/teams/${teamId}/campaign-summary-timeseries?timeRange=${timeRange}`);
      if (!response.ok) {
        throw new Error('Failed to fetch campaign engagement data');
      }
      return response.json();
    },
    enabled: !!teamId,
  });

  // Transform the raw data into chart-friendly format
  const { data, mediaItems } = rawData ? transformTimeseriesData(rawData) : { data: [], mediaItems: [] };

  // Debug logging
  console.log('Campaign Engagement Debug:', {
    timeRange,
    rawDataLength: rawData?.length || 0,
    rawDataSample: rawData?.slice(0, 3),
    transformedDataLength: data.length,
    transformedDataSample: data.slice(0, 3),
    mediaItemsCount: mediaItems.length,
    mediaItems: mediaItems,
    hasNonZeroData: data.some(item =>
      Object.keys(item).some(key => key !== 'name' && item[key] > 0)
    )
  });

  return {
    data,
    mediaItems,
    isLoading,
    error,
  };
}

function transformTimeseriesData(rawData: RawTimeseriesData[]): {
  data: CampaignEngagementData[],
  mediaItems: MediaInfo[]
} {
  if (!rawData || rawData.length === 0) {
    return { data: [], mediaItems: [] };
  }

  // Get unique media names, filtering out null/undefined values
  const mediaNames = Array.from(new Set(
    rawData
      .map(item => item?.media_name)
      .filter(name => name && typeof name === 'string')
  ));

  // Create color palette for media items
  const colors = [
    'hsl(var(--chart-1))',
    'hsl(var(--chart-2))',
    'hsl(var(--chart-3))',
    'hsl(var(--chart-4))',
    'hsl(var(--chart-5))',
    '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00',
    '#ff00ff', '#00ffff', '#ffff00', '#ff0000', '#0000ff'
  ];

  const mediaItems: MediaInfo[] = mediaNames.map((name, index) => ({
    name,
    color: colors[index % colors.length]
  }));

  // Get unique periods, filtering out null/undefined values
  const periods = Array.from(new Set(
    rawData
      .map(item => item?.period)
      .filter(period => period && typeof period === 'string')
  ));

  const sortedPeriods = sortPeriods(periods);

  // Transform data into chart format
  const transformedData: CampaignEngagementData[] = sortedPeriods.map(period => {
    const periodData: CampaignEngagementData = { name: period };

    // Add impression count for each media item for this period
    mediaNames.forEach(mediaName => {
      const impressions = rawData
        .filter(item => item?.period === period && item?.media_name === mediaName)
        .reduce((sum, item) => sum + (item?.total_impressions || 0), 0);

      periodData[mediaName] = impressions;
    });

    return periodData;
  });

  return { data: transformedData, mediaItems };
}

function sortPeriods(periods: string[]): string[] {
  // Filter out undefined/null values and ensure we have strings
  const validPeriods = periods.filter(p => p && typeof p === 'string');

  if (validPeriods.length === 0) {
    return [];
  }

  // Sort periods based on their type
  const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const weekOrder = ['Week1', 'Week2', 'Week3', 'Week4'];
  const monthOrder = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December'];

  // Check what type of periods we have
  if (validPeriods.some(p => dayOrder.includes(p))) {
    // Week view - sort by day order
    return validPeriods.sort((a, b) => dayOrder.indexOf(a) - dayOrder.indexOf(b));
  } else if (validPeriods.some(p => p.startsWith('Week'))) {
    // Month view - sort by week order
    return validPeriods.sort((a, b) => {
      const aIndex = weekOrder.indexOf(a);
      const bIndex = weekOrder.indexOf(b);
      // If not found in weekOrder, try to extract number from Week4, Week3, etc.
      const aNum = aIndex !== -1 ? aIndex : parseInt(a.replace('Week', '')) - 1;
      const bNum = bIndex !== -1 ? bIndex : parseInt(b.replace('Week', '')) - 1;
      return aNum - bNum;
    });
  } else {
    // Year view - sort by month order
    return validPeriods.sort((a, b) => monthOrder.indexOf(a) - monthOrder.indexOf(b));
  }
}


