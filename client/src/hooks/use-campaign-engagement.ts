import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

export interface CampaignEngagementData {
  name: string;
  impressions: number;
  views?: number;
}

export function useCampaignEngagement(teamId: string, timeRange: 'week' | 'month' | 'year') {
  const {
    data: rawData,
    isLoading,
    error,
  } = useQuery<any[]>({
    queryKey: [`/api/teams/${teamId}/campaign-log-summary`, timeRange],
    queryFn: () => apiRequest(`/api/teams/${teamId}/campaign-log-summary?timeRange=${timeRange}`),
    enabled: !!teamId,
  });

  // Transform the raw data into chart-friendly format
  const data: CampaignEngagementData[] = rawData ? transformData(rawData, timeRange) : [];

  return {
    data,
    isLoading,
    error,
  };
}

function transformData(rawData: any[], timeRange: 'week' | 'month' | 'year'): CampaignEngagementData[] {
  if (!rawData || rawData.length === 0) {
    return getDefaultData(timeRange);
  }

  switch (timeRange) {
    case 'week':
      return transformWeekData(rawData);
    case 'month':
      return transformMonthData(rawData);
    case 'year':
      return transformYearData(rawData);
    default:
      return getDefaultData(timeRange);
  }
}

function transformWeekData(rawData: any[]): CampaignEngagementData[] {
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const weekData: CampaignEngagementData[] = [];

  // Create a map for quick lookup
  const dataMap = new Map<number, number>();
  rawData.forEach(item => {
    if (item.log_dow !== undefined && item.total_impressions) {
      const dow = parseInt(item.log_dow);
      const existing = dataMap.get(dow) || 0;
      dataMap.set(dow, existing + parseInt(item.total_impressions));
    }
  });

  // Generate data for each day of the week
  for (let i = 1; i <= 7; i++) {
    const dayIndex = i === 7 ? 0 : i; // Convert to Sunday = 0 format
    weekData.push({
      name: dayNames[dayIndex],
      impressions: dataMap.get(i) || 0,
      views: Math.floor((dataMap.get(i) || 0) * 0.6), // Simulate views as 60% of impressions
    });
  }

  return weekData;
}

function transformMonthData(rawData: any[]): CampaignEngagementData[] {
  const monthData: CampaignEngagementData[] = [];
  const weekMap = new Map<number, number>();

  rawData.forEach(item => {
    if (item.log_week !== undefined && item.total_impressions) {
      const week = parseInt(item.log_week);
      const existing = weekMap.get(week) || 0;
      weekMap.set(week, existing + parseInt(item.total_impressions));
    }
  });

  // Generate data for weeks in the current month
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  
  for (let week = 1; week <= 4; week++) {
    const impressions = weekMap.get(week) || 0;
    monthData.push({
      name: `Week ${week}`,
      impressions,
      views: Math.floor(impressions * 0.6),
    });
  }

  return monthData;
}

function transformYearData(rawData: any[]): CampaignEngagementData[] {
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const yearData: CampaignEngagementData[] = [];
  const monthMap = new Map<number, number>();

  rawData.forEach(item => {
    if (item.log_month !== undefined && item.total_impressions) {
      const month = parseInt(item.log_month);
      const existing = monthMap.get(month) || 0;
      monthMap.set(month, existing + parseInt(item.total_impressions));
    }
  });

  // Generate data for each month
  for (let month = 1; month <= 12; month++) {
    const impressions = monthMap.get(month) || 0;
    yearData.push({
      name: monthNames[month - 1],
      impressions,
      views: Math.floor(impressions * 0.6),
    });
  }

  return yearData;
}

function getDefaultData(timeRange: 'week' | 'month' | 'year'): CampaignEngagementData[] {
  // Return sample data when no real data is available
  switch (timeRange) {
    case 'week':
      return [
        { name: "Mon", impressions: 2400, views: 1440 },
        { name: "Tue", impressions: 1398, views: 839 },
        { name: "Wed", impressions: 9800, views: 5880 },
        { name: "Thu", impressions: 3908, views: 2345 },
        { name: "Fri", impressions: 4800, views: 2880 },
        { name: "Sat", impressions: 3800, views: 2280 },
        { name: "Sun", impressions: 4300, views: 2580 },
      ];
    case 'month':
      return [
        { name: "Week 1", impressions: 9800, views: 5880 },
        { name: "Week 2", impressions: 12000, views: 7200 },
        { name: "Week 3", impressions: 10300, views: 6180 },
        { name: "Week 4", impressions: 13500, views: 8100 },
      ];
    case 'year':
      return [
        { name: "Jan", impressions: 45000, views: 27000 },
        { name: "Feb", impressions: 52000, views: 31200 },
        { name: "Mar", impressions: 58000, views: 34800 },
        { name: "Apr", impressions: 63000, views: 37800 },
        { name: "May", impressions: 61000, views: 36600 },
        { name: "Jun", impressions: 67000, views: 40200 },
        { name: "Jul", impressions: 72000, views: 43200 },
        { name: "Aug", impressions: 75000, views: 45000 },
        { name: "Sep", impressions: 78000, views: 46800 },
        { name: "Oct", impressions: 82000, views: 49200 },
        { name: "Nov", impressions: 88000, views: 52800 },
        { name: "Dec", impressions: 93000, views: 55800 },
      ];
    default:
      return [];
  }
}
