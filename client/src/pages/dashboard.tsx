import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { StatsCard } from "@/components/dashboard/stats-card";
import { ContentEngagementChart } from "@/components/dashboard/content-engagement-chart";
import { CampaignItem } from "@/components/dashboard/campaign-item";
import { ScreenStatus } from "@/components/dashboard/screen-status";
import { ActivityItem } from "@/components/dashboard/activity-item";
import { useAuth } from "@/hooks/use-auth";
import { useCampaigns } from "@/hooks/use-campaigns";
import { useScreens } from "@/hooks/use-screens";
import { useMedia } from "@/hooks/use-media";

import {
  Monitor,
  Presentation,
  Image,
  Play,
  CloudUpload,
  PlayCircle,
  Computer,
  UserPlus
} from "lucide-react";

export default function Dashboard() {
  const { user } = useAuth();
  const [teamId, setTeamId] = useState<string>("");
  
  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    enabled: !!user?.id,
  });
  
  useEffect(() => {
    if (teams && teams.length > 0) {
      setTeamId(teams[0].id);
    }
  }, [teams]);
  
  const { campaigns, isLoading: campaignsLoading } = useCampaigns(teamId);
  const { screens, isLoading: screensLoading } = useScreens(teamId);
  const { mediaItems, isLoading: mediaLoading } = useMedia(teamId);
  
  const activeCampaigns = campaigns?.filter(c => c.status === "active") || [];
  const totalScreens = screens?.length || 0;
  const onlineScreens = screens?.filter(s => s.status === "online").length || 0;
  const offlineScreens = totalScreens - onlineScreens;
  const totalMedia = mediaItems?.length || 0;
  const totalPlays = 145300; // Example data
  
  const isLoading = campaignsLoading || screensLoading || mediaLoading;
  
  if (!teamId && !isLoading) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
          <h2 className="text-2xl font-bold mb-4">Welcome to AdLoopr</h2>
          <p className="text-muted-foreground text-center max-w-md mb-6">
            You don't have any teams yet. Please contact your administrator to get started.
          </p>
        </div>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard
          title="Active Screens"
          value={onlineScreens}
          icon={<Monitor />}
          iconBgColor="bg-blue-50"
          iconColor="text-blue-500"
          trend={{ value: 8, timeframe: "last week", isPositive: true }}
        />
        
        <StatsCard
          title="Active Campaigns"
          value={activeCampaigns.length}
          icon={<Presentation />}
          iconBgColor="bg-purple-50"
          iconColor="text-purple-500"
          trend={{ value: 3, timeframe: "last week", isPositive: true }}
        />
        
        <StatsCard
          title="Media Files"
          value={totalMedia}
          icon={<Image />}
          iconBgColor="bg-amber-50"
          iconColor="text-amber-500"
          subtitle={mediaItems && mediaItems.length > 0 
            ? `${Math.round(mediaItems.reduce((acc, item) => acc + (item.fileSize || 0), 0) / (1024 * 1024))} MB used` 
            : "0 MB used"}
        />
        
        <StatsCard
          title="Total Plays"
          value={totalPlays.toLocaleString()}
          icon={<Play />}
          iconBgColor="bg-green-50"
          iconColor="text-green-500"
          trend={{ value: 12, timeframe: "last month", isPositive: true }}
        />
      </div>
      
      {/* Content Engagement Chart */}
      <div className="mb-8">
        <ContentEngagementChart />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Active Campaigns */}
        <div className="lg:col-span-2 space-y-6">
          <h3 className="text-lg font-semibold">Active Campaigns</h3>
          
          {activeCampaigns.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Presentation className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No active campaigns</h3>
              <p className="text-muted-foreground mb-4">
                Create a campaign to start displaying content on your screens.
              </p>
              <a href="/campaigns" className="text-secondary font-medium">
                Create Campaign
              </a>
            </div>
          ) : (
            <div className="space-y-4">
              {activeCampaigns.slice(0, 3).map((campaign) => (
                <CampaignItem
                  key={campaign.id}
                  name={campaign.name}
                  startDate={campaign.startDate}
                  endDate={campaign.endDate}
                  status={"active"}
                  screenCount={5} // Example data
                  mediaCount={3} // Example data
                />
              ))}
            </div>
          )}
        </div>
        
        {/* Screen Status & Recent Activity */}
        <div className="space-y-8">
          {/* Screen Status */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Screen Status</h3>
            <ScreenStatus online={onlineScreens} offline={offlineScreens} />
          </div>
          
          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
            
            <div className="space-y-4">
              <ActivityItem
                icon={CloudUpload}
                iconBgColor="bg-blue-100"
                iconColor="text-blue-500"
                message={<><span className="font-medium">You</span> uploaded 3 new media files</>}
                timestamp="10 minutes ago"
              />
              
              <ActivityItem
                icon={PlayCircle}
                iconBgColor="bg-green-100"
                iconColor="text-green-500"
                message={<><span className="font-medium">Campaign:</span> Summer Promotion started</>}
                timestamp="2 hours ago"
              />
              
              <ActivityItem
                icon={Computer}
                iconBgColor="bg-amber-100"
                iconColor="text-amber-500"
                message={<><span className="font-medium">Screen:</span> Lobby Display went offline</>}
                timestamp="Yesterday at 8:30 PM"
              />
              
              <ActivityItem
                icon={UserPlus}
                iconBgColor="bg-purple-100"
                iconColor="text-purple-500"
                message={<><span className="font-medium">Team:</span> Sarah joined your team</>}
                timestamp="Yesterday at 2:14 PM"
              />
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
