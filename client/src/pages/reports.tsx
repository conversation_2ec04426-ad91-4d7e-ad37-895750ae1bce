import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { useAuth } from "@/hooks/use-auth";
import { format, subDays } from "date-fns";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";

// Sample data for the charts - in a real app this would come from the API
const weeklyPlayData = [
  { day: "Mon", impressions: 4000, playCount: 2400 },
  { day: "Tue", impressions: 3000, playCount: 1398 },
  { day: "Wed", impressions: 2000, playCount: 3800 },
  { day: "Thu", impressions: 2780, playCount: 3908 },
  { day: "Fri", impressions: 1890, playCount: 4800 },
  { day: "Sat", impressions: 2390, playCount: 3800 },
  { day: "Sun", impressions: 3490, playCount: 4300 },
];

const monthlyPlayData = Array.from({ length: 30 }, (_, i) => ({
  date: format(subDays(new Date(), 30 - i - 1), "MMM dd"),
  impressions: Math.floor(Math.random() * 10000),
  playCount: Math.floor(Math.random() * 8000),
}));

const mediaTypeData = [
  { name: "Images", value: 65 },
  { name: "Videos", value: 25 },
  { name: "Slideshows", value: 10 },
];

const screenLocationData = [
  { name: "Lobby", value: 35 },
  { name: "Sales Floor", value: 25 },
  { name: "Meeting Rooms", value: 15 },
  { name: "Cafeteria", value: 10 },
  { name: "Hallways", value: 15 },
];

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

export default function Reports() {
  const { user } = useAuth();
  const [teamId, setTeamId] = useState<string>("");
  const [dateRange, setDateRange] = useState<string>("7days");
  
  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    enabled: !!user?.id,
  });
  
  useEffect(() => {
    if (teams && teams.length > 0) {
      setTeamId(teams[0].id);
    }
  }, [teams]);

  // These would be real API calls in a production app
  const { data: campaignStats, isLoading: campaignStatsLoading } = useQuery({
    queryKey: [`/api/teams/${teamId}/stats/campaigns`],
    enabled: false, // Disabled since this is just a placeholder
  });

  const { data: screenStats, isLoading: screenStatsLoading } = useQuery({
    queryKey: [`/api/teams/${teamId}/stats/screens`],
    enabled: false, // Disabled since this is just a placeholder
  });

  const isLoading = campaignStatsLoading || screenStatsLoading;

  return (
    <MainLayout>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Analytics & Reports</h2>
          <p className="text-muted-foreground">Track performance of your digital signage content</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Select defaultValue={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="quarter">Last quarter</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total Impressions</CardDescription>
            <CardTitle className="text-3xl">145.3K</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-green-500 mr-1">↑ 12%</span> vs. previous period
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Active Campaigns</CardDescription>
            <CardTitle className="text-3xl">12</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-green-500 mr-1">↑ 3</span> new this period
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Content Plays</CardDescription>
            <CardTitle className="text-3xl">32.4K</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-green-500 mr-1">↑ 8%</span> vs. previous period
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Screen Uptime</CardDescription>
            <CardTitle className="text-3xl">98.2%</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-red-500 mr-1">↓ 0.3%</span> vs. previous period
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Tab Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="screens">Screens</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle>Impressions & Plays Over Time</CardTitle>
                <CardDescription>
                  {dateRange === "7days" ? "Last 7 days" : "Last 30 days"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={dateRange === "7days" ? weeklyPlayData : monthlyPlayData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey={dateRange === "7days" ? "day" : "date"} />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="impressions"
                        stroke="hsl(var(--chart-1))"
                        activeDot={{ r: 8 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="playCount"
                        stroke="hsl(var(--chart-2))"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Content Type Distribution</CardTitle>
                  <CardDescription>Breakdown by media type</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[250px] flex items-center">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={mediaTypeData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={80}
                          fill="#8884d8"
                          paddingAngle={5}
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {mediaTypeData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Screen Location Distribution</CardTitle>
                  <CardDescription>Impressions by location</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[250px] flex items-center">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={screenLocationData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={80}
                          fill="#8884d8"
                          paddingAngle={5}
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {screenLocationData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Daily Engagement</CardTitle>
              <CardDescription>Impressions and plays by day</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={weeklyPlayData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="impressions" fill="hsl(var(--chart-1))" name="Impressions" />
                    <Bar dataKey="playCount" fill="hsl(var(--chart-2))" name="Plays" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="campaigns">
          <Card>
            <CardHeader>
              <CardTitle>Campaign Performance</CardTitle>
              <CardDescription>Comparing impressions across active campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    layout="vertical"
                    data={[
                      { name: "Summer Promotion", impressions: 32500, playCount: 18700 },
                      { name: "New Product Launch", impressions: 24200, playCount: 14300 },
                      { name: "Holiday Special", impressions: 19800, playCount: 12100 },
                      { name: "Back to School", impressions: 17300, playCount: 10200 },
                      { name: "Grand Opening", impressions: 15600, playCount: 9500 },
                      { name: "Weekly Deals", impressions: 14200, playCount: 8300 },
                      { name: "Flash Sale", impressions: 12800, playCount: 7600 },
                    ]}
                    margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} horizontal={false} />
                    <XAxis type="number" />
                    <YAxis type="category" dataKey="name" width={100} />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="impressions" fill="hsl(var(--chart-1))" name="Impressions" />
                    <Bar dataKey="playCount" fill="hsl(var(--chart-2))" name="Plays" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="screens">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Screen Uptime</CardTitle>
                <CardDescription>Average uptime percentage by screen</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      layout="vertical"
                      data={[
                        { name: "Lobby Display", uptime: 99.8 },
                        { name: "Reception Area", uptime: 99.2 },
                        { name: "Sales Floor East", uptime: 98.5 },
                        { name: "Sales Floor West", uptime: 97.8 },
                        { name: "Meeting Room A", uptime: 96.2 },
                        { name: "Meeting Room B", uptime: 93.5 },
                        { name: "Cafeteria", uptime: 98.9 },
                      ]}
                      margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} horizontal={false} />
                      <XAxis type="number" domain={[90, 100]} />
                      <YAxis type="category" dataKey="name" width={100} />
                      <Tooltip formatter={(value) => [`${value}%`, "Uptime"]} />
                      <Bar dataKey="uptime" fill="hsl(var(--chart-3))" name="Uptime %" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Screen Engagement</CardTitle>
                <CardDescription>Impressions by screen location</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { name: "Lobby", impressions: 42500 },
                        { name: "Sales Floor", impressions: 38200 },
                        { name: "Meeting Rooms", impressions: 22800 },
                        { name: "Cafeteria", impressions: 18300 },
                        { name: "Hallways", impressions: 23600 },
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="impressions" fill="hsl(var(--chart-4))" name="Impressions" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="content">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Content</CardTitle>
                <CardDescription>Media with highest engagement</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      layout="vertical"
                      data={[
                        { name: "Summer Promo Video", impressions: 28500, clicks: 4200 },
                        { name: "New Product Image", impressions: 22200, clicks: 3600 },
                        { name: "Weekly Sale Banner", impressions: 18800, clicks: 2900 },
                        { name: "Brand Story Video", impressions: 15300, clicks: 2200 },
                        { name: "Holiday Special", impressions: 14600, clicks: 1800 },
                        { name: "Customer Testimonial", impressions: 12200, clicks: 1400 },
                        { name: "Team Introduction", impressions: 9800, clicks: 890 },
                      ]}
                      margin={{ top: 20, right: 30, left: 120, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} horizontal={false} />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="name" width={120} />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="impressions" fill="hsl(var(--chart-2))" name="Impressions" />
                      <Bar dataKey="clicks" fill="hsl(var(--chart-5))" name="Interactions" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Content Type Performance</CardTitle>
                <CardDescription>Engagement by content type</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { name: "Videos", impressions: 62500, engagement: 12.4 },
                        { name: "Images", impressions: 48200, engagement: 8.7 },
                        { name: "Slideshows", impressions: 32800, engagement: 15.2 },
                        { name: "Text Only", impressions: 18300, engagement: 4.3 },
                        { name: "Interactive", impressions: 15600, engagement: 22.6 },
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="name" />
                      <YAxis yAxisId="left" orientation="left" />
                      <YAxis yAxisId="right" orientation="right" unit="%" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="impressions" fill="hsl(var(--chart-1))" name="Impressions" />
                      <Bar yAxisId="right" dataKey="engagement" fill="hsl(var(--chart-3))" name="Engagement %" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </MainLayout>
  );
}
