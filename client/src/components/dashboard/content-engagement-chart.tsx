import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

// Sample data for the chart
const weekData = [
  { name: "<PERSON>", views: 4000, impressions: 2400 },
  { name: "<PERSON><PERSON>", views: 3000, impressions: 1398 },
  { name: "Wed", views: 2000, impressions: 9800 },
  { name: "Thu", views: 2780, impressions: 3908 },
  { name: "Fri", views: 1890, impressions: 4800 },
  { name: "<PERSON><PERSON>", views: 2390, impressions: 3800 },
  { name: "<PERSON>", views: 3490, impressions: 4300 },
];

const monthData = [
  { name: "Week 1", views: 15000, impressions: 9800 },
  { name: "Week 2", views: 18000, impressions: 12000 },
  { name: "Week 3", views: 16500, impressions: 10300 },
  { name: "Week 4", views: 19200, impressions: 13500 },
];

const yearData = [
  { name: "Jan", views: 65000, impressions: 45000 },
  { name: "Feb", views: 72000, impressions: 52000 },
  { name: "<PERSON>", views: 79000, impressions: 58000 },
  { name: "Apr", views: 85000, impressions: 63000 },
  { name: "May", views: 81000, impressions: 61000 },
  { name: "Jun", views: 87000, impressions: 67000 },
  { name: "Jul", views: 93000, impressions: 72000 },
  { name: "Aug", views: 96000, impressions: 75000 },
  { name: "Sep", views: 98000, impressions: 78000 },
  { name: "Oct", views: 102000, impressions: 82000 },
  { name: "Nov", views: 110000, impressions: 88000 },
  { name: "Dec", views: 118000, impressions: 93000 },
];

type TimeRange = "week" | "month" | "year";

export function ContentEngagementChart() {
  const [timeRange, setTimeRange] = useState<TimeRange>("week");

  const getData = () => {
    switch (timeRange) {
      case "week":
        return weekData;
      case "month":
        return monthData;
      case "year":
        return yearData;
      default:
        return weekData;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <CardTitle>Content Engagement</CardTitle>
          <div className="flex space-x-2">
            <Button
              variant={timeRange === "week" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("week")}
            >
              Week
            </Button>
            <Button
              variant={timeRange === "month" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("month")}
            >
              Month
            </Button>
            <Button
              variant={timeRange === "year" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("year")}
            >
              Year
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={getData()}
              margin={{
                top: 10,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.3} />
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
                dy={10}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
                width={40}
              />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="views"
                stroke="hsl(var(--chart-1))"
                fill="hsl(var(--chart-1))"
                fillOpacity={0.2}
                activeDot={{ r: 8 }}
              />
              <Area
                type="monotone"
                dataKey="impressions"
                stroke="hsl(var(--chart-2))"
                fill="hsl(var(--chart-2))"
                fillOpacity={0.2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
