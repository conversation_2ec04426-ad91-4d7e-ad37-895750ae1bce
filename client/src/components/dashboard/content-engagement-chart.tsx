import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveC<PERSON><PERSON>, Legend } from "recharts";
import { useCampaignEngagement } from "@/hooks/use-campaign-engagement";

type TimeRange = "week" | "month" | "year";

interface ContentEngagementChartProps {
  teamId: string;
}

export function ContentEngagementChart({ teamId }: ContentEngagementChartProps) {
  const [timeRange, setTimeRange] = useState<TimeRange>("week");

  const { data, mediaItems, isLoading, error } = useCampaignEngagement(teamId, timeRange);

  // Debug chart data
  console.log('🎨 Chart Component Data:', {
    dataLength: data.length,
    mediaItemsLength: mediaItems.length,
    sampleDataPoint: data[0],
    mediaItemNames: mediaItems.map(m => ({ key: m.name, display: m.displayName }))
  });

  if (error) {
    console.error('Error loading campaign engagement data:', error);
  }



  return (
    <Card>
      <CardHeader className="pb-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <CardTitle>Content Engagement</CardTitle>
          <div className="flex space-x-2">
            <Button
              variant={timeRange === "week" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("week")}
            >
              Week
            </Button>
            <Button
              variant={timeRange === "month" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("month")}
            >
              Month
            </Button>
            <Button
              variant={timeRange === "year" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("year")}
            >
              Year
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px] w-full">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-sm text-muted-foreground">Loading engagement data...</div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={data}
                margin={{
                  top: 10,
                  right: 30,
                  left: 0,
                  bottom: 0,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.3} />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12 }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12 }}
                  width={40}
                />
                <Tooltip
                  formatter={(value: number, name: string) => {
                    // Find the display name for this sanitized key
                    const mediaItem = mediaItems.find(item => item.name === name);
                    const displayName = mediaItem?.displayName || name;
                    return [value, displayName];
                  }}
                  labelFormatter={(label: string) => `Period: ${label}`}
                />
                <Legend
                  formatter={(value: string) => {
                    // Find the display name for this sanitized key
                    const mediaItem = mediaItems.find(item => item.name === value);
                    return mediaItem?.displayName || value;
                  }}
                />
                {mediaItems.map((media, index) => (
                  <Line
                    key={media.name}
                    type="monotone"
                    dataKey={media.name}
                    stroke={media.color}
                    strokeWidth={3}
                    dot={{ r: 5, fill: media.color }}
                    activeDot={{ r: 8, fill: media.color }}
                    connectNulls={false}
                  />
                ))}
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
