import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON> } from "recharts";
import { useCampaignEngagement } from "@/hooks/use-campaign-engagement";

type TimeRange = "week" | "month" | "year";

interface ContentEngagementChartProps {
  teamId: string;
}

export function ContentEngagementChart({ teamId }: ContentEngagementChartProps) {
  const [timeRange, setTimeRange] = useState<TimeRange>("week");

  const { data, mediaItems, isLoading, error } = useCampaignEngagement(teamId, timeRange);

  // Debug chart data
  console.log('🎨 Chart Component Data:', {
    dataLength: data.length,
    mediaItemsLength: mediaItems.length,
    sampleDataPoint: data[0],
    lastDataPoint: data[data.length - 1],
    mediaItemNames: mediaItems.map(m => ({ key: m.name, display: m.displayName })),
    allData: data
  });

  // Test data to verify chart can render lines
  const testData = [
    { name: "Week1", test1: 0, test2: 0 },
    { name: "Week2", test1: 0, test2: 0 },
    { name: "Week3", test1: 0, test2: 0 },
    { name: "Week4", test1: 700, test2: 500 }
  ];
  const testMediaItems = [
    { name: "test1", displayName: "Test 1", color: "#8884d8" },
    { name: "test2", displayName: "Test 2", color: "#82ca9d" }
  ];

  // Debug key matching and data values
  if (data.length > 0 && mediaItems.length > 0) {
    console.log('🔑 Key Matching Debug:');
    console.log('Data keys in Week4:', Object.keys(data[3] || {}));
    console.log('MediaItem names:', mediaItems.map(m => m.name));
    console.log('Do keys match?', mediaItems.every(m =>
      data[3] && Object.prototype.hasOwnProperty.call(data[3], m.name)
    ));
    console.log('📊 Full data structure for chart:');
    data.forEach((item, index) => {
      console.log(`Week${index + 1}:`, item);
    });
  }

  // Use real data now that we know chart works and keys match
  const chartData = data;
  const chartMediaItems = mediaItems;

  if (error) {
    console.error('Error loading campaign engagement data:', error);
  }



  return (
    <Card>
      <CardHeader className="pb-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <CardTitle>Content Engagement</CardTitle>
          <div className="flex space-x-2">
            <Button
              variant={timeRange === "week" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("week")}
            >
              Week
            </Button>
            <Button
              variant={timeRange === "month" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("month")}
            >
              Month
            </Button>
            <Button
              variant={timeRange === "year" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("year")}
            >
              Year
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px] w-full">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-sm text-muted-foreground">Loading engagement data...</div>
            </div>
          ) : chartData.length === 0 || chartMediaItems.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-sm text-muted-foreground">No data available</div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                key={`${timeRange}-${chartData.length}-${chartMediaItems.length}`}
                data={chartData}
                margin={{
                  top: 10,
                  right: 30,
                  left: 0,
                  bottom: 0,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.3} />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12 }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12 }}
                  width={40}
                  domain={[0, 'dataMax']}
                />
                <Tooltip
                  formatter={(value: number, name: string) => {
                    // Find the display name for this sanitized key
                    const mediaItem = chartMediaItems.find(item => item.name === name);
                    const displayName = mediaItem?.displayName || name;
                    return [value, displayName];
                  }}
                  labelFormatter={(label: string) => `Period: ${label}`}
                />
                <Legend
                  formatter={(value: string) => {
                    // Find the display name for this sanitized key
                    const mediaItem = chartMediaItems.find(item => item.name === value);
                    return mediaItem?.displayName || value;
                  }}
                />
                {chartMediaItems.map((media, index) => {
                  const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300'];
                  const color = colors[index % colors.length];
                  console.log(`🎨 Rendering line for ${media.displayName} with color ${color}`);
                  return (
                    <Line
                      key={media.name}
                      type="monotone"
                      dataKey={media.name}
                      stroke={color}
                      strokeWidth={3}
                      strokeOpacity={1}
                      dot={{ r: 5, fill: color, strokeWidth: 2, stroke: color }}
                      activeDot={{ r: 8, fill: color, strokeWidth: 2, stroke: '#fff' }}
                      connectNulls={true}
                      isAnimationActive={false}
                    />
                  );
                })}
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
