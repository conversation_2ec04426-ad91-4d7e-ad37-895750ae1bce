import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { useCampaignEngagement } from "@/hooks/use-campaign-engagement";

type TimeRange = "week" | "month" | "year";

interface ContentEngagementChartProps {
  teamId: string;
}

export function ContentEngagementChart({ teamId }: ContentEngagementChartProps) {
  const [timeRange, setTimeRange] = useState<TimeRange>("week");

  const { data, mediaItems, isLoading, error } = useCampaignEngagement(teamId, timeRange);



  if (error) {
    console.error('Error loading campaign engagement data:', error);
  }



  return (
    <Card>
      <CardHeader className="pb-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <CardTitle>Content Engagement</CardTitle>
          <div className="flex space-x-2">
            <Button
              variant={timeRange === "week" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("week")}
            >
              Week
            </Button>
            <Button
              variant={timeRange === "month" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("month")}
            >
              Month
            </Button>
            <Button
              variant={timeRange === "year" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setTimeRange("year")}
            >
              Year
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px] w-full">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-sm text-muted-foreground">Loading engagement data...</div>
            </div>
          ) : data.length === 0 || mediaItems.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-sm text-muted-foreground">No data available</div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                key={`${timeRange}-${data.length}-${mediaItems.length}`}
                data={data}
                margin={{
                  top: 10,
                  right: 30,
                  left: 0,
                  bottom: 0,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.3} />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12 }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12 }}
                  width={40}
                  domain={[0, 'dataMax']}
                />
                <Tooltip
                  formatter={(value: number, name: string) => {
                    // Find the display name for this sanitized key
                    const mediaItem = mediaItems.find(item => item.name === name);
                    const displayName = mediaItem?.displayName || name;
                    return [value, displayName];
                  }}
                  labelFormatter={(label: string) => `Period: ${label}`}
                />
                <Legend
                  formatter={(value: string) => {
                    // Find the display name for this sanitized key
                    const mediaItem = mediaItems.find(item => item.name === value);
                    return mediaItem?.displayName || value;
                  }}
                />
                {mediaItems.map((media, index) => {
                  const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300'];
                  const color = colors[index % colors.length];
                  return (
                    <Line
                      key={media.name}
                      type="monotone"
                      dataKey={media.name}
                      stroke={color}
                      strokeWidth={3}
                      strokeOpacity={1}
                      dot={{ r: 5, fill: color, strokeWidth: 2, stroke: color }}
                      activeDot={{ r: 8, fill: color, strokeWidth: 2, stroke: '#fff' }}
                      connectNulls={true}
                      isAnimationActive={false}
                    />
                  );
                })}
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
